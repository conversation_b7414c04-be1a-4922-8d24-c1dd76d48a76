%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 468a46d0ae32c3544b7d98094e6448a9, type: 3}
  m_Name: AddressableAssetSettings
  m_EditorClassIdentifier: 
  m_DefaultGroup: f50ed049a1785314ba9a0955d645914f
  m_CachedHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_OptimizeCatalogSize: 0
  m_BuildRemoteCatalog: 0
  m_BundleLocalCatalog: 0
  m_CatalogRequestsTimeout: 0
  m_DisableCatalogUpdateOnStart: 0
  m_IgnoreUnsupportedFilesInBuild: 0
  m_UniqueBundleIds: 0
  m_NonRecursiveBuilding: 1
  m_CCDEnabled: 0
  m_maxConcurrentWebRequests: 500
  m_ContiguousBundles: 1
  m_StripUnityVersionFromBundleBuild: 0
  m_DisableVisibleSubAssetRepresentations: 0
  m_ShaderBundleNaming: 0
  m_ShaderBundleCustomNaming: 
  m_MonoScriptBundleNaming: 0
  m_MonoScriptBundleCustomNaming: 
  m_RemoteCatalogBuildPath:
    m_Id: 
  m_RemoteCatalogLoadPath:
    m_Id: 
  m_ContentStateBuildPath: 
  m_BuildAddressablesWithPlayerBuild: 0
  m_overridePlayerVersion: 
  m_GroupAssets:
  - {fileID: 11400000, guid: cd3b918afd40a8a40a666329fd77da03, type: 2}
  - {fileID: 11400000, guid: 3acefb7ea21452f49b93c6a6e927ec39, type: 2}
  - {fileID: 11400000, guid: f1160158d95a0ea4a8162c3e2c8abd79, type: 2}
  - {fileID: 11400000, guid: f3c64577da4399c4891adaa66b937894, type: 2}
  - {fileID: 11400000, guid: 626e76e231ac04d4fa3a5f9346b11600, type: 2}
  - {fileID: 11400000, guid: 5f56968b897014743b31ccf5e8006810, type: 2}
  - {fileID: 11400000, guid: 5c931fdf733bd954d86e74ebe6817587, type: 2}
  m_BuildSettings:
    m_CompileScriptsInVirtualMode: 0
    m_CleanupStreamingAssetsAfterBuilds: 1
    m_LogResourceManagerExceptions: 1
    m_BundleBuildPath: Temp/com.unity.addressables/AssetBundles
  m_ProfileSettings:
    m_Profiles:
    - m_InheritedParent: 
      m_Id: 44c1f1377fce1aa4b852e03d6b9372e7
      m_ProfileName: Default
      m_Values:
      - m_Id: 531e251e0cf6d4a43acbf5c109bfb5f3
        m_Value: '[UnityEditor.EditorUserBuildSettings.activeBuildTarget]'
      - m_Id: 92b886882bfe81c48b95929832f35b94
        m_Value: '[UnityEngine.AddressableAssets.Addressables.BuildPath]/[BuildTarget]'
      - m_Id: e2e887f0d0551694b94ce5e0aa329992
        m_Value: '{UnityEngine.AddressableAssets.Addressables.RuntimePath}/[BuildTarget]'
      - m_Id: 8e0e67b3e13eb30429e30ca7d4b7c4c2
        m_Value: ServerData/[BuildTarget]
      - m_Id: 897af97c5a073d449b2701520051588d
        m_Value: http://[PrivateIpAddress]:[HostingServicePort]
    m_ProfileEntryNames:
    - m_Id: 531e251e0cf6d4a43acbf5c109bfb5f3
      m_Name: BuildTarget
      m_InlineUsage: 0
    - m_Id: 92b886882bfe81c48b95929832f35b94
      m_Name: Local.BuildPath
      m_InlineUsage: 0
    - m_Id: e2e887f0d0551694b94ce5e0aa329992
      m_Name: Local.LoadPath
      m_InlineUsage: 0
    - m_Id: 8e0e67b3e13eb30429e30ca7d4b7c4c2
      m_Name: Remote.BuildPath
      m_InlineUsage: 0
    - m_Id: 897af97c5a073d449b2701520051588d
      m_Name: Remote.LoadPath
      m_InlineUsage: 0
    m_ProfileVersion: 1
  m_LabelTable:
    m_LabelNames:
    - default
    - Locale
    - Locale-zh
    - Preload
    - Locale-en
    - Locale-zh-CN
  m_SchemaTemplates: []
  m_GroupTemplateObjects:
  - {fileID: 11400000, guid: 69d802521db0c4e4698315b16c971a2e, type: 2}
  m_InitializationObjects: []
  m_CertificateHandlerType:
    m_AssemblyName: 
    m_ClassName: 
  m_ActivePlayerDataBuilderIndex: 3
  m_DataBuilders:
  - {fileID: 11400000, guid: 255273edf147eb243a2dd0664e6371fa, type: 2}
  - {fileID: 11400000, guid: 885b1a514bb10964088e1fab4e97b9f5, type: 2}
  - {fileID: 11400000, guid: c18b6ea1083c5fc428d5c08602e09261, type: 2}
  - {fileID: 11400000, guid: 9c5a056348ba32543adb03e919155b72, type: 2}
  m_ActiveProfileId: 44c1f1377fce1aa4b852e03d6b9372e7
  m_HostingServicesManager:
    m_HostingServiceInfos: []
    m_Settings: {fileID: 11400000}
    m_NextInstanceId: 0
    m_RegisteredServiceTypeRefs: []
