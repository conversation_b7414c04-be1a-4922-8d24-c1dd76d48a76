fileFormatVersion: 2
guid: cf34af7132ba19445bd1ee68214f97b8
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #28
    second: {fileID: 2100000, guid: 1ef4adbc38b9d9c48962710e75e10b35, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #44
    second: {fileID: 2100000, guid: e195f1b4837eb4c488cb8004c3e52c2e, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #45
    second: {fileID: 2100000, guid: f096d0405b60f334e9bc5f94a118b094, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #47
    second: {fileID: 2100000, guid: 39dd1f44c8a41b2418b450f65516acaf, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #48
    second: {fileID: 2100000, guid: fa0ced9ac149c104cbeb5d15c47f9d05, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #52
    second: {fileID: 2100000, guid: 5f78e286f3bc70d45a8566542b927909, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #53
    second: {fileID: 2100000, guid: 5722dff1a9cc64143a6c710a6e7420b8, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #69
    second: {fileID: 2100000, guid: bbd448422ee2b8d438add00c22ae3f00, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #71
    second: {fileID: 2100000, guid: a45fe61aa8b07c646980abbc46fe7200, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #74
    second: {fileID: 2100000, guid: 226d07df2c5664c4f84f17796fc5a775, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #83
    second: {fileID: 2100000, guid: 9e6f33709ce869b4f9bb4136fa48eb76, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u4E0D\u9508\u94A2"
    second: {fileID: 2100000, guid: b8c66a223d233d042a5fd2786f831811, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5415\u5708"
    second: {fileID: 2100000, guid: 09dc0a588b902a04883c4d257ba2da0e, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5916\u58F3"
    second: {fileID: 2100000, guid: 31fb20c98b2fc7c499432c4d47e17b7f, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5BC6\u5C01\u5708"
    second: {fileID: 2100000, guid: 5805fb13674bb344aa01d8b2ebd5043d, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5F39\u7C27"
    second: {fileID: 2100000, guid: daa29d1cbbf771e48aca263eb4fef941, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u6267\u884C\u5668"
    second: {fileID: 2100000, guid: 09e180376da7dd94d805af0cea2d780d, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u6807\u5FD7"
    second: {fileID: 2100000, guid: a59143ca38d5d4c4cb325a0b405d6194, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u87BA\u6813"
    second: {fileID: 2100000, guid: 6745e527174eb43439919cab6ef0c85d, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94A2\u5708"
    second: {fileID: 2100000, guid: 5da98f0bd65d76f4cb0a459aacfb91c4, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94C1\u5708"
    second: {fileID: 2100000, guid: ed139736c2a15b5428e633a387e0fc88, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94C1\u7BA1"
    second: {fileID: 2100000, guid: 7243453939790514c8bfa5c353862c2f, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94DD\u5408\u91D1"
    second: {fileID: 2100000, guid: a9bce0ac416afa34c88d2131ecad0100, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94DD\u6746"
    second: {fileID: 2100000, guid: 06a2c2d1e571afa449f4a190db2154c3, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u9600\u5E3D"
    second: {fileID: 2100000, guid: 5ac0b17ed0e0e5d408d54b24c334f0d9, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u9632\u706B\u77F3\u58A8"
    second: {fileID: 2100000, guid: 865216cdc45fcbd46a7ca547f047561c, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u9ED1\u94C1\u5E95\u5EA7"
    second: {fileID: 2100000, guid: 75cc7ea454d53a347982944ca75528cd, type: 2}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
