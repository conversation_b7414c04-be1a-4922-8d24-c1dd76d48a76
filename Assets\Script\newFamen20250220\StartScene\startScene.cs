using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class startScene : MonoBehaviour
{
    // Start is called before the first frame update
    void Start()
    {
        
    }

    GameObject go;
    Vector3 mousePos;
    Ray ray;
    RaycastHit hit;
    [SerializeField]  GraphicRaycaster raycaster;
    [SerializeField] EventSystem eventSystem;
    // Update is called once per frame
    void Update()
    {
        //检查鼠标是不是点在ui上
        PointerEventData pointerData = new PointerEventData(eventSystem);
        pointerData.position = Input.mousePosition;
        List<RaycastResult> results = new List<RaycastResult>();
        raycaster.Raycast(pointerData, results);

        if (results.Count > 0)
        {
            Debug.Log("点击在UI上：" + results[0].gameObject.name);
            return;
        }

        //当鼠标放在游戏对象物体时 对象开始旋转
        //获取鼠标在屏幕上的位置
        mousePos = Input.mousePosition;
        //将屏幕坐标转换为射线
        ray = Camera.main.ScreenPointToRay(mousePos);
        if (Physics.Raycast(ray, out hit))
        {
            //获取射线碰撞到的物体
            go = hit.collider.gameObject;
            //旋转物体
            if(go.name == "40寸球阀带动画01")
            {
                go.transform.Rotate(Vector3.back, 0.4f);
            }else if(go.name == "16寸清管阀拆分")
            {
                go.transform.Rotate(Vector3.down, 0.4f);
            }else if(go.name == "止回阀"){
                go.transform.Rotate(Vector3.down, 0.4f);
            }
        }

        
        
        //当鼠标点击游戏对象物体时 场景跳转
        if (Input.GetMouseButtonDown(0) && go != null)
        {
            if (go.name == "40寸球阀带动画01")
            {
                SceneManager.LoadScene("Scenes/new球阀/famen");
            }
            else if (go.name == "16寸清管阀拆分")
            {
                SceneManager.LoadScene("Scenes/new球阀/famen2");
            }
            else if (go.name == "止回阀")
            {
                SceneManager.LoadScene("Scenes/new球阀/famen3");
            }
        }
        
        go = null;
    }
}
