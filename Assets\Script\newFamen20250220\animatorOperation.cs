using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System.IO;
using Newtonsoft.Json;
using UnityEngine.SceneManagement;
using UnityEngine.Localization.Settings;
using UnityEngine.SocialPlatforms;
using UnityEngine.Localization;

public class animatorOperation : MonoBehaviour
{
    [SerializeField] Button openQiufaBtn, closeQiufaBtn, expandtBtn, stopBtn, ReverseBtn, kaimenBtn, guanmenBtn;
    [SerializeField] Button action1Btn, action2Btn, action3Btn, action4Btn;
    [SerializeField] public Animator mainAnimator;
    [SerializeField] departOperation departOperation;
    [SerializeField] RuntimeAnimatorController famenAnimatorController1;
    [SerializeField] RuntimeAnimatorController famenAnimatorController2;

    [SerializeField] RuntimeAnimatorController banpouFamenController1;
    [SerializeField] RuntimeAnimatorController banpouFamenController2;

    [SerializeField] RuntimeAnimatorController animatorController1;
    [SerializeField] RuntimeAnimatorController animatorController2;

    [SerializeField] RuntimeAnimatorController kaimenController1;
    [SerializeField] RuntimeAnimatorController guanmenController2;

    [SerializeField] public List<GameObject> waikeGameobjects;
    [SerializeField] Slider speedSlider;
    [SerializeField] Text speedText;
    string sceneName = "";

    float presentSpeed = 3;
    void Start()
    {
        sceneName = SceneManager.GetActiveScene().name;

        //根据当前场景名 获取保存的动画播放速度
        if (PlayerPrefs.HasKey(sceneName + "_animatorSpeed"))
        {
            presentSpeed = PlayerPrefs.GetFloat(sceneName + "_animatorSpeed");
            speedSlider.value = presentSpeed;
            speedText.text = presentSpeed.ToString();
        }

        //根据开场设置的是不是i自动播放拆分动画 和 声音 决定要不要播放动画和声音
        if (PlayerPrefs.HasKey("_autoPlayAnimate") && PlayerPrefs.GetFloat("_autoPlayAnimate") > 0)
        { 
            
        }


        //快开 针对 清阀
        if (kaimenBtn != null)
        {
            kaimenBtn.onClick.AddListener(() =>
            {
                departOperation.restore();

                //如果waikeGameobjects不为空,则设置透明
                if (waikeGameobjects.Count > 0)
                {
                    Color NotTransparentColor;
                    foreach (var item in waikeGameobjects)
                    {
                        for (int i = 0; i < item.GetComponent<MeshRenderer>().materials.Length; i++)
                        {
                            OperationBar.SetMaterial.SetMaterialRenderingMode(item.GetComponent<MeshRenderer>().materials[i], OperationBar.SetMaterial.RenderingMode.Fade);
                            NotTransparentColor = item.GetComponent<MeshRenderer>().materials[i].color;
                            item.GetComponent<MeshRenderer>().materials[i].color = new Color(NotTransparentColor.r, NotTransparentColor.g, NotTransparentColor.b, 0.3f);
                        }
                    }
                }

                if (departOperation.banpouModel.activeSelf && departOperation.banpouModel.GetComponent<Animator>() != null)
                {
                    StopAllCoroutines();
                    setPause(false);
                    departOperation.banpouModel.GetComponent<Animator>().runtimeAnimatorController = kaimenController1;
                    departOperation.banpouModel.GetComponent<Animator>().SetBool("go", true);
                }
                else
                {
                    StopAllCoroutines();
                    setPause(false);

                    mainAnimator.runtimeAnimatorController = kaimenController1;
                    mainAnimator.SetBool("go", true);
                }

            });
        }

        //快关  针对 清阀
        if (guanmenBtn != null)
        {
            guanmenBtn.onClick.AddListener(() =>
            {
                departOperation.restore();

                //如果waikeGameobjects不为空,则设置透明
                if (waikeGameobjects.Count > 0)
                {
                    Color NotTransparentColor;
                    foreach (var item in waikeGameobjects)
                    {
                        for (int i = 0; i < item.GetComponent<MeshRenderer>().materials.Length; i++)
                        {
                            OperationBar.SetMaterial.SetMaterialRenderingMode(item.GetComponent<MeshRenderer>().materials[i], OperationBar.SetMaterial.RenderingMode.Fade);
                            NotTransparentColor = item.GetComponent<MeshRenderer>().materials[i].color;
                            item.GetComponent<MeshRenderer>().materials[i].color = new Color(NotTransparentColor.r, NotTransparentColor.g, NotTransparentColor.b, 0.3f);
                        }
                    }
                }

                if (departOperation.banpouModel.activeSelf && departOperation.banpouModel.GetComponent<Animator>() != null)
                {
                    StopAllCoroutines();
                    setPause(false);
                    departOperation.banpouModel.GetComponent<Animator>().runtimeAnimatorController = guanmenController2;
                    departOperation.banpouModel.GetComponent<Animator>().SetBool("go", true);
                }
                else
                {
                    StopAllCoroutines();
                    setPause(false);
                    mainAnimator.runtimeAnimatorController = guanmenController2;
                    mainAnimator.SetBool("go", true);
                }
            });
        }

        speedSlider.onValueChanged.AddListener((float value) =>
        {
            presentSpeed = value;
            mainAnimator.speed = presentSpeed;
            speedText.text = value.ToString();
            PlayerPrefs.SetFloat(sceneName + "_animatorSpeed", value);
            PlayerPrefs.Save();
        });

        openQiufaBtn.onClick.AddListener(() =>
        {
            StopAllCoroutines();
            setPause(false);

            departOperation.restore();

            //如果waikeGameobjects不为空,则设置透明
            if (waikeGameobjects.Count > 0)
            {
                Color NotTransparentColor;
                foreach (var item in waikeGameobjects)
                {
                    for (int i = 0; i < item.GetComponent<MeshRenderer>().materials.Length; i++)
                    {
                        OperationBar.SetMaterial.SetMaterialRenderingMode(item.GetComponent<MeshRenderer>().materials[i], OperationBar.SetMaterial.RenderingMode.Fade);
                        NotTransparentColor = item.GetComponent<MeshRenderer>().materials[i].color;
                        item.GetComponent<MeshRenderer>().materials[i].color = new Color(NotTransparentColor.r, NotTransparentColor.g, NotTransparentColor.b, 0.3f);
                    }
                }
            }

            //谁显示 就播放谁的动画
            if (departOperation.banpouModel.activeSelf && departOperation.banpouModel.GetComponent<Animator>() != null)
            {
                var presentAnimator = departOperation.banpouModel.GetComponent<Animator>();
                AnimatorStateInfo stateInfo = presentAnimator.GetCurrentAnimatorStateInfo(0);
                float presentNormalTime = stateInfo.normalizedTime;
                float startNormalizedTime = 0;

                if (stateInfo.IsName("banpouState"))
                {
                    startNormalizedTime = 1 - presentNormalTime <= 0 ? 0 : 1 - presentNormalTime;
                }

                presentAnimator.runtimeAnimatorController = banpouFamenController1;
                stateInfo = presentAnimator.GetCurrentAnimatorStateInfo(0);
                presentAnimator.Play("banpouState", 0, startNormalizedTime);
                presentAnimator.SetBool("go", true);
            }
            else
            {
                //切换显示完整模型
                departOperation.banpouModel.SetActive(false);
                mainAnimator.gameObject.SetActive(true);

                //如果是止回阀 就 透明外壳
                if (sceneName == "famen3" && waikeGameobjects.Count > 0)
                {
                    Color NotTransparentColor;
                    foreach (var item in waikeGameobjects)
                    {
                        for (int i = 0; i < item.GetComponent<MeshRenderer>().materials.Length; i++)
                        {
                            OperationBar.SetMaterial.SetMaterialRenderingMode(item.GetComponent<MeshRenderer>().materials[i], OperationBar.SetMaterial.RenderingMode.Fade);
                            NotTransparentColor = item.GetComponent<MeshRenderer>().materials[i].color;
                            item.GetComponent<MeshRenderer>().materials[i].color = new Color(NotTransparentColor.r, NotTransparentColor.g, NotTransparentColor.b, 0.3f);
                        }
                    }
                }

                AnimatorStateInfo stateInfo = mainAnimator.GetCurrentAnimatorStateInfo(0);
                float presentNormalTime = stateInfo.normalizedTime;
                float startNormalizedTime = 0;
                if (stateInfo.IsName("banpouState"))
                {
                    startNormalizedTime = 1 - presentNormalTime <= 0 ? 0 : 1 - presentNormalTime;
                }

                mainAnimator.runtimeAnimatorController = famenAnimatorController1;
                stateInfo = mainAnimator.GetCurrentAnimatorStateInfo(0);
                mainAnimator.Play("banpouState", 0, startNormalizedTime);
                mainAnimator.SetBool("go", true);
            }


            //外壳透明
            // Color NotTransparentColor;
            // if (waikeGameobjects.Count > 0)
            // {
            //     foreach (var item in waikeGameobjects)
            //     {
            //         for (int i = 0; i < item.GetComponent<MeshRenderer>().materials.Length; i++)
            //         {
            //             OperationBar.SetMaterial.SetMaterialRenderingMode(item.GetComponent<MeshRenderer>().materials[i], OperationBar.SetMaterial.RenderingMode.Fade);
            //             NotTransparentColor = item.GetComponent<MeshRenderer>().materials[i].color;
            //             item.GetComponent<MeshRenderer>().materials[i].color = new Color(NotTransparentColor.r, NotTransparentColor.g, NotTransparentColor.b, 0.3f);
            //         }
            //     }
            // }



        });

        closeQiufaBtn.onClick.AddListener(() =>
        {
            StopAllCoroutines();
            setPause(false);
            departOperation.restore();

            //如果waikeGameobjects不为空,则设置透明
            if (waikeGameobjects.Count > 0)
            {
                Color NotTransparentColor;
                foreach (var item in waikeGameobjects)
                {
                    for (int i = 0; i < item.GetComponent<MeshRenderer>().materials.Length; i++)
                    {
                        OperationBar.SetMaterial.SetMaterialRenderingMode(item.GetComponent<MeshRenderer>().materials[i], OperationBar.SetMaterial.RenderingMode.Fade);
                        NotTransparentColor = item.GetComponent<MeshRenderer>().materials[i].color;
                        item.GetComponent<MeshRenderer>().materials[i].color = new Color(NotTransparentColor.r, NotTransparentColor.g, NotTransparentColor.b, 0.3f);
                    }
                }
            }

            if (departOperation.banpouModel.activeSelf && departOperation.banpouModel.GetComponent<Animator>() != null)
            {
                var presentAnimator = departOperation.banpouModel.GetComponent<Animator>();
                AnimatorStateInfo stateInfo = presentAnimator.GetCurrentAnimatorStateInfo(0);
                float presentNormalTime = stateInfo.normalizedTime;
                float startNormalizedTime = 0;

                if (stateInfo.IsName("banpouState"))
                {
                    startNormalizedTime = 1 - presentNormalTime <= 0 ? 0 : 1 - presentNormalTime;
                }

                presentAnimator.runtimeAnimatorController = banpouFamenController2;
                stateInfo = presentAnimator.GetCurrentAnimatorStateInfo(0);
                presentAnimator.Play("banpouState", 0, startNormalizedTime);
                presentAnimator.SetBool("go", true);
            }
            else
            {
                //如果是止回阀 就 透明外壳
                if (sceneName == "famen3" && waikeGameobjects.Count > 0)
                {
                    Color NotTransparentColor;
                    foreach (var item in waikeGameobjects)
                    {
                        for (int i = 0; i < item.GetComponent<MeshRenderer>().materials.Length; i++)
                        {
                            OperationBar.SetMaterial.SetMaterialRenderingMode(item.GetComponent<MeshRenderer>().materials[i], OperationBar.SetMaterial.RenderingMode.Fade);
                            NotTransparentColor = item.GetComponent<MeshRenderer>().materials[i].color;
                            item.GetComponent<MeshRenderer>().materials[i].color = new Color(NotTransparentColor.r, NotTransparentColor.g, NotTransparentColor.b, 0.3f);
                        }
                    }
                }

                //切换显示完整模型
                departOperation.banpouModel.SetActive(false);
                mainAnimator.gameObject.SetActive(true);

                AnimatorStateInfo stateInfo = mainAnimator.GetCurrentAnimatorStateInfo(0);
                float presentNormalTime = stateInfo.normalizedTime;
                float startNormalizedTime = 0;

                if (stateInfo.IsName("banpouState"))
                {
                    startNormalizedTime = 1 - presentNormalTime <= 0 ? 0 : 1 - presentNormalTime;
                }
                mainAnimator.runtimeAnimatorController = famenAnimatorController2;
                stateInfo = mainAnimator.GetCurrentAnimatorStateInfo(0);
                mainAnimator.Play("banpouState", 0, startNormalizedTime);
                mainAnimator.SetBool("go", true);
            }

            //外壳透明
            //Color NotTransparentColor;
            // foreach (var item in waikeGameobjects)
            // {
            //     for (int i = 0; i < item.GetComponent<MeshRenderer>().materials.Length; i++)
            //     {
            //         OperationBar.SetMaterial.SetMaterialRenderingMode(item.GetComponent<MeshRenderer>().materials[i], OperationBar.SetMaterial.RenderingMode.Fade);
            //         NotTransparentColor = item.GetComponent<MeshRenderer>().materials[i].color;
            //         item.GetComponent<MeshRenderer>().materials[i].color = new Color(NotTransparentColor.r, NotTransparentColor.g, NotTransparentColor.b, 0.3f);
            //     }
            // }
            mainAnimator.SetBool("go", true);
        });

        expandtBtn.onClick.AddListener(() =>
        {
            AnimatorStateInfo stateInfo = mainAnimator.GetCurrentAnimatorStateInfo(0);
            float presentNormalTime = stateInfo.normalizedTime;
            float startNormalizedTime = 0;
            setPause(false);
            if (stateInfo.IsName("ReverseAnimaState"))
            {
                startNormalizedTime = 1 - presentNormalTime <= 0 ? 0 : 1 - presentNormalTime;
            }

            StopAllCoroutines();
            departOperation.restore();

            //如果waikeGameobjects不为空,则设置透明
            if (waikeGameobjects.Count > 0 && sceneName == "famen3")
            {
                Color NotTransparentColor;
                foreach (var item in waikeGameobjects)
                {
                    for (int i = 0; i < item.GetComponent<MeshRenderer>().materials.Length; i++)
                    {
                        OperationBar.SetMaterial.SetMaterialRenderingMode(item.GetComponent<MeshRenderer>().materials[i], OperationBar.SetMaterial.RenderingMode.Fade);
                        NotTransparentColor = item.GetComponent<MeshRenderer>().materials[i].color;
                        item.GetComponent<MeshRenderer>().materials[i].color = new Color(NotTransparentColor.r, NotTransparentColor.g, NotTransparentColor.b, 0.3f);
                    }
                }
            }
            mainAnimator.runtimeAnimatorController = animatorController1;
            stateInfo = mainAnimator.GetCurrentAnimatorStateInfo(0);
            mainAnimator.Play("animaState", 0, startNormalizedTime);
            mainAnimator.SetBool("go", true);

        });

        stopBtn.onClick.AddListener(() =>
        {
            if (mainAnimator.speed == 0)
            {
                setPause(false);
                return;
            }
            setPause(true);
        });

        ReverseBtn.onClick.AddListener(() =>
        {
            setPause(false);
            StopAllCoroutines();
            departOperation.restore();
            //如果waikeGameobjects不为空,则设置透明
            if (waikeGameobjects.Count > 0 && sceneName == "famen3")
            {
                Color NotTransparentColor;
                foreach (var item in waikeGameobjects)
                {
                    for (int i = 0; i < item.GetComponent<MeshRenderer>().materials.Length; i++)
                    {
                        OperationBar.SetMaterial.SetMaterialRenderingMode(item.GetComponent<MeshRenderer>().materials[i], OperationBar.SetMaterial.RenderingMode.Fade);
                        NotTransparentColor = item.GetComponent<MeshRenderer>().materials[i].color;
                        item.GetComponent<MeshRenderer>().materials[i].color = new Color(NotTransparentColor.r, NotTransparentColor.g, NotTransparentColor.b, 0.3f);
                    }
                }
            }

            AnimatorStateInfo stateInfo = mainAnimator.GetCurrentAnimatorStateInfo(0);
            float presentNormalTime = stateInfo.normalizedTime;
            float startNormalizedTime = 0;
            if (stateInfo.IsName("animaState"))
            {
                startNormalizedTime = 1 - presentNormalTime <= 0 ? 0 : 1 - presentNormalTime;
            }


            mainAnimator.runtimeAnimatorController = animatorController2;
            stateInfo = mainAnimator.GetCurrentAnimatorStateInfo(0);
            mainAnimator.Play("ReverseAnimaState", 0, startNormalizedTime);
            mainAnimator.SetBool("go", true);
            //开启协程 播放controller2 检查是否播放完毕 播放完毕则切换回原来的动画
            // StartCoroutine(CheckAnimatorDone());
        });

        action1Btn.onClick.AddListener(() =>
        {
            string sceneName = SceneManager.GetActiveScene().name;
            if (sceneName == "famen")
            {
                playAnimSplitor(0.601f, 1f);
                // playAnimSplitor(0, 0.41f);
            }
            if (sceneName == "famen2")
            {
                playAnimSplitor(0.547f, 1f);
            }

            if (sceneName == "famen3")
            {
                playAnimSplitor(0.0f, 0.61f);
                //透明外壳
                if (waikeGameobjects.Count > 0)
                {
                    Color NotTransparentColor;
                    foreach (var item in waikeGameobjects)
                    {
                        for (int i = 0; i < item.GetComponent<MeshRenderer>().materials.Length; i++)
                        {
                            OperationBar.SetMaterial.SetMaterialRenderingMode(item.GetComponent<MeshRenderer>().materials[i], OperationBar.SetMaterial.RenderingMode.Fade);
                            NotTransparentColor = item.GetComponent<MeshRenderer>().materials[i].color;
                            item.GetComponent<MeshRenderer>().materials[i].color = new Color(NotTransparentColor.r, NotTransparentColor.g, NotTransparentColor.b, 0.3f);
                        }
                    }
                }
                
            }
        });

        action2Btn.onClick.AddListener(() =>
        {
            string sceneName = SceneManager.GetActiveScene().name;
            if (sceneName == "famen")
            {
                playAnimSplitor(0.081f, 0.601f);
                // playAnimSplitor(0.41f, 0.632f);
            }
            if (sceneName == "famen2")
            {
                playAnimSplitor(0.289f, 0.547f);
            }

            if (sceneName == "famen3")
            {
                playAnimSplitor(0.61f, 1f);
                //透明外壳
                if (waikeGameobjects.Count > 0)
                {
                    Color NotTransparentColor;
                    foreach (var item in waikeGameobjects)
                    {
                        Debug.Log("vfadverwrtqwertqwer1");
                        for (int i = 0; i < item.GetComponent<MeshRenderer>().materials.Length; i++)
                        {
                            Debug.Log("vfadverwrtqwertqwer2");
                            OperationBar.SetMaterial.SetMaterialRenderingMode(item.GetComponent<MeshRenderer>().materials[i], OperationBar.SetMaterial.RenderingMode.Fade);
                            NotTransparentColor = item.GetComponent<MeshRenderer>().materials[i].color;
                            item.GetComponent<MeshRenderer>().materials[i].color = new Color(NotTransparentColor.r, NotTransparentColor.g, NotTransparentColor.b, 0.3f);
                        }
                    }
                }
                
            }
        });

        if (action3Btn != null)
        {
            action3Btn.onClick.AddListener(() =>
            {
                string sceneName = SceneManager.GetActiveScene().name;
                // if (sceneName == "famen")
                // {
                //     playAnimSplitor(0.136f, 0.325f);
                //     // playAnimSplitor(0.632f, 0.844f);
                // }
                if (sceneName == "famen2")
                {
                    playAnimSplitor(0.081f, 0.289f);
                }

            });
        }
        if (action4Btn != null)
        {
            action4Btn.onClick.AddListener(() =>
            {
                string sceneName = SceneManager.GetActiveScene().name;
                if (sceneName == "famen")
                {
                    playAnimSplitor(0, 0.136f);
                    // playAnimSplitor(0.926f, 1f);
                }
                if (sceneName == "famen2")
                {
                    playAnimSplitor(0, 0.081f);
                }

            });
        }
    }

    public void setPause(bool isStop = true)
    {
        mainAnimator.speed = isStop ? 0 : presentSpeed;
        if (LocalizationSettings.SelectedLocale.Identifier.Code != null && LocalizationSettings.SelectedLocale.Identifier.Code == "en")
        {
            stopBtn.GetComponentInChildren<Text>().text = isStop ? "Continue" : "Pause";
        }
        else
        { 
            stopBtn.GetComponentInChildren<Text>().text = isStop ? "继续" : "暂停";
        }
        
    }

    [SerializeField] Transform zhezhaoTransform;
    //autoPlay
    public void autoPlay()
    {
        //摄像机移动到左上角

        //开始播放动画

        //开始播放音频

        //开启遮罩，播放完毕之前所有按钮都不管用
        if(zhezhaoTransform!=null)zhezhaoTransform.gameObject.SetActive(true);
        //检查播放完成 取消遮罩。

    }


    public void removaRuntimeAnimatorController()
    {
        StopAllCoroutines();
        setPause(false);
        mainAnimator.SetBool("go", false);

        //if (mainAnimator.runtimeAnimatorController != null) mainAnimator.runtimeAnimatorController = null;
    }

    public void playAnimSplitor(float startNormalizedTime, float endNormalizedTime)
    {
        setPause(false);
        departOperation.restore();
        if (mainAnimator.runtimeAnimatorController != animatorController2) mainAnimator.runtimeAnimatorController = animatorController2;
        mainAnimator.SetBool("go", true);
        mainAnimator.Play("ReverseAnimaState", 0, startNormalizedTime);
        StartCoroutine(checkAnimatorDone(endNormalizedTime));
    }

    IEnumerator checkAnimatorDone(float endNormalizedTime)
    {
        do
        {
            if (mainAnimator.runtimeAnimatorController == null)
            {
                mainAnimator.speed = presentSpeed;
                break;
            }

            if (mainAnimator.GetCurrentAnimatorStateInfo(0).normalizedTime >= endNormalizedTime)
            {
                mainAnimator.speed = 0;
                break;
            }
            yield return null;
        } while (true);
    }
}
