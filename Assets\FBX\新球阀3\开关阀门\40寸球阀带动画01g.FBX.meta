fileFormatVersion: 2
guid: 09e7f227f55098645a734a86fcd24100
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #28
    second: {fileID: 2100000, guid: e3ec15428da65b54ba47375ae98a781f, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #44
    second: {fileID: 2100000, guid: 1a17dc8dc9acace4180265709b3b91a6, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #45
    second: {fileID: 2100000, guid: 1ec60121b89a3be4a82b57461f6a495a, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #47
    second: {fileID: 2100000, guid: 641f986ceb2650d439f2bd7705260d1a, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #48
    second: {fileID: 2100000, guid: 4a8f51149ddc60c4b98e288ab6126d74, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #52
    second: {fileID: 2100000, guid: 53de919321d5b1c439392f4fc61aadb5, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #53
    second: {fileID: 2100000, guid: c7d24f32ea2e8914491771a026d66d9c, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #69
    second: {fileID: 2100000, guid: 0b47733ca6276da46925c4776224dd3e, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #71
    second: {fileID: 2100000, guid: 43510597fc1c58946b53e02c4a13c2ae, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #74
    second: {fileID: 2100000, guid: 07199b278c08e1946b00ae668446cea0, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u4E0D\u9508\u94A2"
    second: {fileID: 2100000, guid: b311a3df83a13af4c9f55013aeff1f64, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5415\u5708"
    second: {fileID: 2100000, guid: f1b1237b94a0c634b9854742411ed7b6, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5916\u58F3"
    second: {fileID: 2100000, guid: 3371cb70ca5abb0488084ebedeac9dad, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5BC6\u5C01\u5708"
    second: {fileID: 2100000, guid: fbbb6e2536e26234ebbc12913663a519, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5F39\u7C27"
    second: {fileID: 2100000, guid: 12e5e9d6eb370f94cb07f12f6a421892, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u6267\u884C\u5668"
    second: {fileID: 2100000, guid: f458206051c83d44da7b2eb2aa3a5d98, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u6807\u5FD7"
    second: {fileID: 2100000, guid: db7593fd3216d374a987e5427f26370f, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u87BA\u6813"
    second: {fileID: 2100000, guid: 57568237735c1e045b1a671d7d482ab3, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94A2\u5708"
    second: {fileID: 2100000, guid: 04d5916de83165b46ad1197d3b8e5957, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94C1\u5708"
    second: {fileID: 2100000, guid: 69a5500df1876ea41adf596325f4c4b8, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94C1\u7BA1"
    second: {fileID: 2100000, guid: 39b7817117f6a6743949fc19b6a035c8, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94DD\u5408\u91D1"
    second: {fileID: 2100000, guid: c94fb2e24b682d84f83d98ca9a3c118b, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94DD\u6746"
    second: {fileID: 2100000, guid: 6ed209d9c64d7d245ac76817c51a3471, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u9600\u5E3D"
    second: {fileID: 2100000, guid: 6d4b771ebffb9b545825d1c79af739b0, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u9632\u706B\u77F3\u58A8"
    second: {fileID: 2100000, guid: 14c88cbcd7ea753459a88598122b4cf7, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u9ED1\u94C1\u5E95\u5EA7"
    second: {fileID: 2100000, guid: 4a644e5d32a31aa409f82dedef022e26, type: 2}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
