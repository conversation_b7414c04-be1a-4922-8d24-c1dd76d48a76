fileFormatVersion: 2
guid: 37200db489b131049b40c9d63cdcf501
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #27
    second: {fileID: 2100000, guid: 7768b8b21642d3b4ebe0e424efcb6e60, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #28
    second: {fileID: 2100000, guid: 31e0925abf3216948997cca7cb8291e7, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #39
    second: {fileID: 2100000, guid: cdb1022da58a4814089cb5dfdecf3047, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #44
    second: {fileID: 2100000, guid: dfaa34e84085e6d46a64a8585245f07e, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #45
    second: {fileID: 2100000, guid: 5c3f46ab0de044449b59c35bd328a87d, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #47
    second: {fileID: 2100000, guid: 46856a791a50e39478ad38972b3771d3, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #48
    second: {fileID: 2100000, guid: 224ed2c4b8f3eab40bb7136a289a8c39, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #52
    second: {fileID: 2100000, guid: 2911fd805a00e294bbf3dfa886090046, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #53
    second: {fileID: 2100000, guid: b4d2e241ef424ea4ca966ec9ec57d564, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #69
    second: {fileID: 2100000, guid: 0c15c6e43be5fbd48959ca993956af50, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u4E0D\u9508\u94A2"
    second: {fileID: 2100000, guid: b5e9743fafa4c024aa05475af905a18e, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5415\u5708"
    second: {fileID: 2100000, guid: 4592ed66427466744b3c2a62cb6e2c7b, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5916\u58F3"
    second: {fileID: 2100000, guid: ab801c32db308194fad91bd5a4060262, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5BC6\u5C01\u5708"
    second: {fileID: 2100000, guid: 35baff053135d5b499dd8bdf9757ebce, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5F39\u7C27"
    second: {fileID: 2100000, guid: d1d35f32ed739c14c9fed90939f0da69, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u6267\u884C\u5668"
    second: {fileID: 2100000, guid: dcce9bc7a4be2f74ea9969ebc022d6e0, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u6807\u5FD7"
    second: {fileID: 2100000, guid: e962c9c8de0eacd4c925330ab328e4e4, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u87BA\u6813"
    second: {fileID: 2100000, guid: d65b9f8e6524df34b8c355f007b502a4, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94A2\u5708"
    second: {fileID: 2100000, guid: 3a67cb70ab185634482a8ab1285d3842, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94C1\u5708"
    second: {fileID: 2100000, guid: df5c94ffde721e649b1145fcab1abb97, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94C1\u7BA1"
    second: {fileID: 2100000, guid: bdfb93a3cac40164dbf28e982e698c5a, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94DD\u5408\u91D1"
    second: {fileID: 2100000, guid: 8505946909fca68489a8fab026ceca18, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94DD\u6746"
    second: {fileID: 2100000, guid: 203608b777663fe47bc2212dbd80d1a7, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u9600\u5E3D"
    second: {fileID: 2100000, guid: 9cad71f8b2f72604e9eb05e164fd0421, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u9632\u706B\u77F3\u58A8"
    second: {fileID: 2100000, guid: 169209dd564c31741b43ea9b76ad9e73, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u9ED1\u94C1\u5E95\u5EA7"
    second: {fileID: 2100000, guid: cfc574a603c937f4892044d495bdae2d, type: 2}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: defaultAnima
      takeName: Take 001
      internalID: 1827226128182048838
      firstFrame: 0
      lastFrame: 2200
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
