using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using UnityEngine.UI;
using UnityEngine.Localization.Settings;
public class toggleBtn : MonoBehaviour
{
    [SerializeField] RectTransform rectTransform;
    [SerializeField] Button toggleBtn1;

    private Vector2 originalPosition;
    void Start()
    {
        originalPosition = rectTransform.anchoredPosition;

        toggleBtn1.onClick.AddListener(() =>
        {
            if (rectTransform.gameObject.activeSelf)
            {

                rectTransform.DOAnchorPos(originalPosition + new Vector2(350, 0), 0.5f).SetEase(Ease.OutBack).onComplete = () =>
                {
                    rectTransform.gameObject.SetActive(false);
                };
                if (LocalizationSettings.SelectedLocale.Identifier.Code != null && LocalizationSettings.SelectedLocale.Identifier.Code == "en")
                { 
                    transform.GetChild(0).GetComponent<Text>().text = "Show Operate Bar";
                }
                else
                {
                    transform.GetChild(0).GetComponent<Text>().text = "显示操作栏";
                }
            }
            else
            {
                rectTransform.gameObject.SetActive(true);
                rectTransform.DOAnchorPos(originalPosition, 0.5f).SetEase(Ease.OutBack);
                if (LocalizationSettings.SelectedLocale.Identifier.Code != null && LocalizationSettings.SelectedLocale.Identifier.Code == "en")
                { 
                    transform.GetChild(0).GetComponent<Text>().text = "Hide Operate Bar";
                }
                else
                {
                    transform.GetChild(0).GetComponent<Text>().text = "隐藏操作栏";
                }
                
                
            }
        });
    }

}
