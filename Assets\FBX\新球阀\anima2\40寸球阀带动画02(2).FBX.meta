fileFormatVersion: 2
guid: 25272493dd0f90b49904b3d7b99b96f7
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #27
    second: {fileID: 2100000, guid: a0cf2ab75aeba904cbb3ad16457da404, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #28
    second: {fileID: 2100000, guid: dda4475a2d2ca4444838a8e437c2a9f4, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #39
    second: {fileID: 2100000, guid: a882801fd992c5e4a9a077527fa95551, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #44
    second: {fileID: 2100000, guid: 3b039567942c3a14bb6a95b6d39a0e66, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #45
    second: {fileID: 2100000, guid: 9292b9542109c9b4d9c429f22b9684e6, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #47
    second: {fileID: 2100000, guid: 2e7f0b4f806e5d34b9bed339a97f6bc4, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #48
    second: {fileID: 2100000, guid: 505116e27030b1b408b7e06bdfdea026, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #52
    second: {fileID: 2100000, guid: a4853dd71de45484c8ab41370ef8b61b, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #53
    second: {fileID: 2100000, guid: 4b7bdfcc444b2714ea472a5a6e383bc7, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material #69
    second: {fileID: 2100000, guid: a14154fa2841eb141b8f55704e7e9c4a, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u4E0D\u9508\u94A2"
    second: {fileID: 2100000, guid: e031583f5beb8774e8721e30ea4015a7, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5415\u5708"
    second: {fileID: 2100000, guid: addff5e0d33e35f4b96d0da8296cdfbf, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5916\u58F3"
    second: {fileID: 2100000, guid: 4deb415690288ef49b407408157dd302, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5BC6\u5C01\u5708"
    second: {fileID: 2100000, guid: 30d2252325ec5dc4dbee043efe211b96, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u5F39\u7C27"
    second: {fileID: 2100000, guid: ef2508730f4b3fc49b9b5b5fbc38051d, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u6267\u884C\u5668"
    second: {fileID: 2100000, guid: d6601d5abbb03044994a2c6431cb369b, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u6807\u5FD7"
    second: {fileID: 2100000, guid: 9e07f20e255bc634b978c8f7c8e16650, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u87BA\u6813"
    second: {fileID: 2100000, guid: dc6bb55b849afcc41a06f634d6f8f3f3, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94A2\u5708"
    second: {fileID: 2100000, guid: 64e9be0d9c9eccf43bd43f1dda5835c0, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94C1\u5708"
    second: {fileID: 2100000, guid: 959f982972ea2c34aab49022ee70d9e3, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94C1\u7BA1"
    second: {fileID: 2100000, guid: 5ec6c784b39fd2f4a83f04d9c8986e50, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94DD\u5408\u91D1"
    second: {fileID: 2100000, guid: 89c53658cc2a13741ab5c3008957b293, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u94DD\u6746"
    second: {fileID: 2100000, guid: f2c32bccb29d02645be81d92b13b33f1, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u9600\u5E3D"
    second: {fileID: 2100000, guid: 55ab853045f698e43a4b0983705ea2d6, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u9632\u706B\u77F3\u58A8"
    second: {fileID: 2100000, guid: d98f7e7f4c4ccd341b9869961d5bb6ef, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: "\u9ED1\u94C1\u5E95\u5EA7"
    second: {fileID: 2100000, guid: 01ebf27a7e7770d49a8b1c0555d173c0, type: 2}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
