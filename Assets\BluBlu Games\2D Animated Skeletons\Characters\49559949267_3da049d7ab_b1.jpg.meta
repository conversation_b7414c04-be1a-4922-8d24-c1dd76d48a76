fileFormatVersion: 2
guid: 2f4e684cb5690c74a8adc53e22b758aa
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 1
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites: []
    outline: []
    customData: 
    physicsShape: []
    bones:
    - name: bone_1
      guid: 19e61e00e13de6345bb61f69e3aa343b
      position: {x: 434.35367, y: 207.10881, z: 0}
      rotation: {x: 0, y: 0, z: 0.6671723, w: 0.74490345}
      length: 169.77725
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4278190335
    - name: bone_2
      guid: 47dc816a210ebd54ba4b53bb895dbce3
      position: {x: 169.77724, y: -0.000007406369, z: 0}
      rotation: {x: 0, y: 0, z: -0.06650693, w: 0.997786}
      length: 133.33081
      parentId: 0
      color:
        serializedVersion: 2
        rgba: 4278255615
    - name: bone_3
      guid: 17e0b285120c0ed45898a4330fd798be
      position: {x: 133.3308, y: 0.0000037119662, z: 0}
      rotation: {x: 0, y: 0, z: 0.03528571, w: 0.99937725}
      length: 138.7166
      parentId: 1
      color:
        serializedVersion: 2
        rgba: 4278255360
    - name: bone_4
      guid: fc8d53f84504cb54cb648cf2c5afc44e
      position: {x: 138.7166, y: 0.000076737255, z: 0}
      rotation: {x: 0, y: 0, z: 0.027204981, w: 0.9996299}
      length: 184.53041
      parentId: 2
      color:
        serializedVersion: 2
        rgba: 4294967040
    - name: bone_5
      guid: 0f61733ecc79d384d9c1da7fbdf78758
      position: {x: 184.53047, y: 0.00000016278682, z: 0}
      rotation: {x: 0, y: 0, z: -0.09724891, w: 0.9952601}
      length: 73.99194
      parentId: 3
      color:
        serializedVersion: 2
        rgba: 4294901760
    - name: bone_6
      guid: 1d9ffc1f40478374c9595955dd589731
      position: {x: 73.99198, y: -0.000009076806, z: 0}
      rotation: {x: 0, y: 0, z: -0.24321789, w: 0.9699717}
      length: 116.41788
      parentId: 4
      color:
        serializedVersion: 2
        rgba: 4294902015
    - name: bone_7
      guid: 2077835b898cf094fbeffd9dc0117a52
      position: {x: 643.48114, y: 500.09436, z: 0}
      rotation: {x: 0, y: 0, z: 0.4008667, w: 0.9161364}
      length: 129.67532
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4278223103
    - name: bone_8
      guid: 05a3fae96b7bc3c449a850736c01762e
      position: {x: 129.67531, y: -0.0000064878186, z: 0}
      rotation: {x: 0, y: 0, z: -0.03505693, w: 0.99938536}
      length: 143.5502
      parentId: 6
      color:
        serializedVersion: 2
        rgba: 4278255488
    - name: bone_9
      guid: 607ff0cb8bb3e824a9f566ae435a04e8
      position: {x: 143.55017, y: -0.000118222786, z: 0}
      rotation: {x: 0, y: 0, z: 0.066096365, w: 0.9978133}
      length: 36.05709
      parentId: 7
      color:
        serializedVersion: 2
        rgba: 4286643968
    - name: bone_10
      guid: 3549ad6ea4788c0419bec2ed04eef939
      position: {x: 349.46033, y: 545.64685, z: 0}
      rotation: {x: 0, y: 0, z: 0.9238795, w: 0.38268355}
      length: 130.30614
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4294934528
    - name: bone_11
      guid: 491c416311255964b8b81758887a1e84
      position: {x: 130.30615, y: 0.000011992983, z: 0}
      rotation: {x: 0, y: 0, z: -0.09191984, w: 0.9957664}
      length: 107.97278
      parentId: 9
      color:
        serializedVersion: 2
        rgba: 4294901888
    - name: bone_12
      guid: 3d9626f5426a942428db60984f2183cb
      position: {x: 107.972725, y: -0.0000118497765, z: 0}
      rotation: {x: 0, y: 0, z: 0.091920294, w: 0.9957664}
      length: 21.961666
      parentId: 10
      color:
        serializedVersion: 2
        rgba: 4286578943
    - name: bone_13
      guid: ba73e35a3775b0d4da8172e4e3547f36
      position: {x: 348.42505, y: 194.6854, z: 0}
      rotation: {x: 0, y: 0, z: -0.67433834, w: 0.7384225}
      length: 57.175465
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4278255615
    - name: bone_14
      guid: f70c66ef1515e814093b33082abc1eb5
      position: {x: 57.17546, y: -0.0000029325956, z: 0}
      rotation: {x: 0, y: 0, z: 0.02205456, w: 0.9997568}
      length: 61.64075
      parentId: 12
      color:
        serializedVersion: 2
        rgba: 4278255360
    - name: bone_15
      guid: 33e8406d4b9ceb94ea38d567339e6719
      position: {x: 588.6111, y: 173.97969, z: 0}
      rotation: {x: 0, y: 0, z: -0.723472, w: 0.69035375}
      length: 66.33091
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4294967040
    - name: bone_16
      guid: fd0913affa70af946bbbbb986de89be5
      position: {x: 66.330894, y: -0.000011574419, z: 0}
      rotation: {x: 0, y: 0, z: 0.0234178, w: 0.99972576}
      length: 61.0818
      parentId: 14
      color:
        serializedVersion: 2
        rgba: 4294901760
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 1537655665
    vertices:
    - {x: 714.8948, y: 250.54088}
    - {x: 735.8644, y: 340.60867}
    - {x: 729.9162, y: 375.8486}
    - {x: 728.944, y: 404.9153}
    - {x: 707.20245, y: 457.02744}
    - {x: 856.2833, y: 650.88873}
    - {x: 878.024, y: 707.05945}
    - {x: 855.2483, y: 745.6262}
    - {x: 771.3908, y: 702.40955}
    - {x: 640.94476, y: 603.28436}
    - {x: 618.1687, y: 738.13574}
    - {x: 618.1687, y: 761.0376}
    - {x: 618.9385, y: 872.8482}
    - {x: 647.6621, y: 920.4715}
    - {x: 656.71545, y: 962.9182}
    - {x: 638.85236, y: 989.8354}
    - {x: 600.2844, y: 982.5882}
    - {x: 526.515, y: 918.3982}
    - {x: 475.5221, y: 845.9246}
    - {x: 460.76364, y: 825.17914}
    - {x: 460.7011, y: 810.6127}
    - {x: 453.4543, y: 787.4885}
    - {x: 407.90216, y: 761.34314}
    - {x: 165.64557, y: 741.4078}
    - {x: 287.80914, y: 477.1482}
    - {x: 261.92703, y: 427.18964}
    - {x: 262.9623, y: 391.7277}
    - {x: 255.71533, y: 352.1226}
    - {x: 268.13864, y: 262.9624}
    - {x: 590.37744, y: 31.058613}
    - {x: 620.6651, y: 43.481956}
    - {x: 647.8468, y: 125.26948}
    - {x: 669.85095, y: 137.69287}
    - {x: 675.2897, y: 199.80994}
    - {x: 275.95517, y: 172.94463}
    - {x: 331.86044, y: 74.59241}
    - {x: 524.42334, y: 110.8274}
    - {x: 287.34323, y: 238.16742}
    - {x: 302.87253, y: 151.20357}
    - {x: 499.57614, y: 150.16826}
    - {x: 545.12915, y: 63.204292}
    - {x: 392.9423, y: 722.6806}
    - {x: 400.18927, y: 672.98694}
    - {x: 380.5189, y: 621.22266}
    - {x: 300.80194, y: 678.1633}
    - {x: 224.19087, y: 742.35126}
    - {x: 497.50604, y: 127.39196}
    - {x: 372.23657, y: 62.169018}
    - {x: 179.67365, y: 760.9863}
    - {x: 174.49722, y: 682.3045}
    - {x: 221.08502, y: 578.77606}
    - {x: 429.1773, y: 159.48576}
    - {x: 428.14206, y: 137.74481}
    - {x: 411.57742, y: 129.46252}
    - {x: 401.22455, y: 93.227554}
    - {x: 387.76587, y: 72.5218}
    indices: 000000000200000001000000020000000400000003000000020000002700000004000000020000002100000027000000040000000900000005000000040000002b0000000900000004000000330000001800000004000000180000002b0000000400000027000000330000000500000008000000060000000500000009000000080000002c00000032000000310000001f0000002800000024000000090000002b0000002a0000000a000000150000000b0000000a0000002a000000150000000b000000120000000c0000000b00000014000000120000000b00000015000000140000000c000000100000000d0000000c00000011000000100000000c00000012000000110000000d000000100000000e0000000e000000100000000f000000000000002100000002000000150000002a00000029000000150000002900000016000000120000001400000013000000170000002d00000031000000180000001a0000001900000018000000330000001a00000018000000320000002b0000001a000000250000001b0000001a00000033000000250000001b000000250000001c0000001d000000280000001e0000001e000000280000001f0000001f00000021000000200000001f0000002700000021000000090000002a0000000a00000006000000080000000700000022000000250000002600000023000000370000002f000000230000002600000035000000230000003500000036000000230000003600000037000000240000002e000000270000002500000033000000260000002600000033000000350000002b000000320000002c0000002c000000310000002d00000017000000300000002d0000001f0000002400000027000000330000003400000035000000
    edges:
    - {x: 0, y: 1}
    - {x: 0, y: 33}
    - {x: 1, y: 2}
    - {x: 2, y: 3}
    - {x: 3, y: 4}
    - {x: 4, y: 5}
    - {x: 5, y: 6}
    - {x: 6, y: 7}
    - {x: 7, y: 8}
    - {x: 8, y: 9}
    - {x: 9, y: 10}
    - {x: 10, y: 11}
    - {x: 11, y: 12}
    - {x: 12, y: 13}
    - {x: 13, y: 14}
    - {x: 14, y: 15}
    - {x: 15, y: 16}
    - {x: 16, y: 17}
    - {x: 17, y: 18}
    - {x: 18, y: 19}
    - {x: 19, y: 20}
    - {x: 20, y: 21}
    - {x: 21, y: 22}
    - {x: 22, y: 41}
    - {x: 23, y: 48}
    - {x: 23, y: 49}
    - {x: 24, y: 25}
    - {x: 24, y: 50}
    - {x: 25, y: 26}
    - {x: 26, y: 27}
    - {x: 27, y: 28}
    - {x: 28, y: 37}
    - {x: 29, y: 30}
    - {x: 29, y: 40}
    - {x: 30, y: 31}
    - {x: 31, y: 32}
    - {x: 32, y: 33}
    - {x: 34, y: 37}
    - {x: 34, y: 38}
    - {x: 35, y: 38}
    - {x: 35, y: 47}
    - {x: 36, y: 40}
    - {x: 36, y: 46}
    - {x: 39, y: 46}
    - {x: 39, y: 51}
    - {x: 41, y: 42}
    - {x: 42, y: 43}
    - {x: 43, y: 44}
    - {x: 44, y: 45}
    - {x: 45, y: 48}
    - {x: 47, y: 55}
    - {x: 49, y: 50}
    - {x: 51, y: 52}
    - {x: 52, y: 53}
    - {x: 53, y: 54}
    - {x: 54, y: 55}
    weights:
    - 'weight[0]': 0.76980543
      'weight[1]': 0.19864944
      'weight[2]': 0.031545177
      'weight[3]': 0
      'boneIndex[0]': 14
      'boneIndex[1]': 6
      'boneIndex[2]': 9
      'boneIndex[3]': 0
    - 'weight[0]': 0.47524112
      'weight[1]': 0.43858406
      'weight[2]': 0.07016269
      'weight[3]': 0.016012127
      'boneIndex[0]': 6
      'boneIndex[1]': 14
      'boneIndex[2]': 9
      'boneIndex[3]': 12
    - 'weight[0]': 0.57512444
      'weight[1]': 0.32383865
      'weight[2]': 0.0827182
      'weight[3]': 0.018318683
      'boneIndex[0]': 6
      'boneIndex[1]': 14
      'boneIndex[2]': 9
      'boneIndex[3]': 12
    - 'weight[0]': 0.67513835
      'weight[1]': 0.23170649
      'weight[2]': 0.059751354
      'weight[3]': 0.033403825
      'boneIndex[0]': 6
      'boneIndex[1]': 14
      'boneIndex[2]': 9
      'boneIndex[3]': 2
    - 'weight[0]': 0.84004766
      'weight[1]': 0.091791406
      'weight[2]': 0.06816094
      'weight[3]': 0
      'boneIndex[0]': 6
      'boneIndex[1]': 1
      'boneIndex[2]': 2
      'boneIndex[3]': 0
    - 'weight[0]': 0.98298836
      'weight[1]': 0.017011708
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 7
      'boneIndex[1]': 6
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 8
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 8
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.78163743
      'weight[1]': 0.21836257
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 8
      'boneIndex[1]': 7
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.94099844
      'weight[1]': 0.04639966
      'weight[2]': 0.012601947
      'weight[3]': 0
      'boneIndex[0]': 2
      'boneIndex[1]': 7
      'boneIndex[2]': 6
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 3
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 3
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.5731072
      'weight[1]': 0.4268928
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 4
      'boneIndex[1]': 5
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 5
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 5
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 5
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 5
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.61944526
      'weight[1]': 0.3805548
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 5
      'boneIndex[1]': 4
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.93019956
      'weight[1]': 0.06980052
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 4
      'boneIndex[1]': 3
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.6554485
      'weight[1]': 0.34455153
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 3
      'boneIndex[1]': 4
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.9868095
      'weight[1]': 0.013190472
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 3
      'boneIndex[1]': 4
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 3
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.8900445
      'weight[1]': 0.07910487
      'weight[2]': 0.030850558
      'weight[3]': 0
      'boneIndex[0]': 3
      'boneIndex[1]': 2
      'boneIndex[2]': 7
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 11
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.5892507
      'weight[1]': 0.31860733
      'weight[2]': 0.09214205
      'weight[3]': 0
      'boneIndex[0]': 1
      'boneIndex[1]': 9
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.49134964
      'weight[1]': 0.27631652
      'weight[2]': 0.15324786
      'weight[3]': 0.07908599
      'boneIndex[0]': 1
      'boneIndex[1]': 9
      'boneIndex[2]': 12
      'boneIndex[3]': 0
    - 'weight[0]': 0.43006963
      'weight[1]': 0.2493128
      'weight[2]': 0.24907172
      'weight[3]': 0.07154586
      'boneIndex[0]': 1
      'boneIndex[1]': 12
      'boneIndex[2]': 9
      'boneIndex[3]': 0
    - 'weight[0]': 0.3727499
      'weight[1]': 0.35865784
      'weight[2]': 0.20807151
      'weight[3]': 0.06052073
      'boneIndex[0]': 12
      'boneIndex[1]': 1
      'boneIndex[2]': 9
      'boneIndex[3]': 0
    - 'weight[0]': 0.76957697
      'weight[1]': 0.13238285
      'weight[2]': 0.075623944
      'weight[3]': 0.022416221
      'boneIndex[0]': 12
      'boneIndex[1]': 1
      'boneIndex[2]': 9
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 15
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 15
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.9597243
      'weight[1]': 0.040275652
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 14
      'boneIndex[1]': 15
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 14
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 14
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.9747343
      'weight[1]': 0.0252657
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 12
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.99999994
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 13
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.8912771
      'weight[1]': 0.108722895
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 14
      'boneIndex[1]': 15
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.95654255
      'weight[1]': 0.028646449
      'weight[2]': 0.014810968
      'weight[3]': 0
      'boneIndex[0]': 12
      'boneIndex[1]': 1
      'boneIndex[2]': 9
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 12
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 14
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 15
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.8152621
      'weight[1]': 0.13639058
      'weight[2]': 0.038021017
      'weight[3]': 0.010326344
      'boneIndex[0]': 3
      'boneIndex[1]': 2
      'boneIndex[2]': 7
      'boneIndex[3]': 6
    - 'weight[0]': 0.757137
      'weight[1]': 0.24286298
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 3
      'boneIndex[1]': 2
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.8616718
      'weight[1]': 0.13832815
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 2
      'boneIndex[1]': 9
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.7269055
      'weight[1]': 0.2730944
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 10
      'boneIndex[1]': 9
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.9628388
      'weight[1]': 0.037161272
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 11
      'boneIndex[1]': 10
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.97755736
      'weight[1]': 0.022442618
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 14
      'boneIndex[1]': 15
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 13
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 11
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 10
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 9
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.79157025
      'weight[1]': 0.1936701
      'weight[2]': 0.01475968
      'weight[3]': 0
      'boneIndex[0]': 12
      'boneIndex[1]': 0
      'boneIndex[2]': 14
      'boneIndex[3]': 0
    - 'weight[0]': 0.472379
      'weight[1]': 0.43804803
      'weight[2]': 0.08957295
      'weight[3]': 0
      'boneIndex[0]': 12
      'boneIndex[1]': 13
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 0.84482455
      'weight[1]': 0.15517536
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 13
      'boneIndex[1]': 12
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 13
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    - 'weight[0]': 1
      'weight[1]': 0
      'weight[2]': 0
      'weight[3]': 0
      'boneIndex[0]': 13
      'boneIndex[1]': 0
      'boneIndex[2]': 0
      'boneIndex[3]': 0
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable: {}
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
