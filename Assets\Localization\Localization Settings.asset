%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a07b5cd0b1b829245bc8c4b6978793e8, type: 3}
  m_Name: Localization Settings
  m_EditorClassIdentifier: 
  m_StartupSelectors:
  - rid: 3133919383155638301
  m_AvailableLocales:
    rid: 3133919383155638279
  m_AssetDatabase:
    rid: 3133919383155638280
  m_StringDatabase:
    rid: 3133919383155638281
  m_Metadata:
    m_Items: []
  m_ProjectLocaleIdentifier:
    m_Code: zh
  m_InitializeSynchronously: 0
  references:
    version: 2
    RefIds:
    - rid: 3133919383155638279
      type: {class: LocalesProvider, ns: UnityEngine.Localization.Settings, asm: Unity.Localization}
      data: 
    - rid: 3133919383155638280
      type: {class: LocalizedAssetDatabase, ns: UnityEngine.Localization.Settings,
        asm: Unity.Localization}
      data:
        m_DefaultTableReference:
          m_TableCollectionName: 
        m_UseFallback: 0
    - rid: 3133919383155638281
      type: {class: LocalizedStringDatabase, ns: UnityEngine.Localization.Settings,
        asm: Unity.Localization}
      data:
        m_DefaultTableReference:
          m_TableCollectionName: 
        m_UseFallback: 0
        m_MissingTranslationState: 1
        m_NoTranslationFoundMessage: No translation found for '{key}' in {table.TableCollectionName}
        m_SmartFormat:
          rid: 3133919383155638282
    - rid: 3133919383155638282
      type: {class: SmartFormatter, ns: UnityEngine.Localization.SmartFormat, asm: Unity.Localization}
      data:
        m_Settings:
          rid: 3133919383155638283
        m_Parser:
          rid: 3133919383155638284
        m_Sources:
        - rid: 3133919383155638285
        - rid: 3133919383155638286
        - rid: 3133919383155638287
        - rid: 3133919383155638288
        - rid: 3133919383155638289
        - rid: 3133919383155638290
        - rid: 3133919383155638291
        m_Formatters:
        - rid: 3133919383155638285
        - rid: 3133919383155638292
        - rid: 3133919383155638293
        - rid: 3133919383155638294
        - rid: 3133919383155638295
        - rid: 3133919383155638296
        - rid: 3133919383155638297
        - rid: 3133919383155638298
        - rid: 3133919383155638299
    - rid: 3133919383155638283
      type: {class: SmartSettings, ns: UnityEngine.Localization.SmartFormat.Core.Settings,
        asm: Unity.Localization}
      data:
        m_FormatErrorAction: 0
        m_ParseErrorAction: 0
        m_CaseSensitivity: 0
        m_ConvertCharacterStringLiterals: 1
    - rid: 3133919383155638284
      type: {class: Parser, ns: UnityEngine.Localization.SmartFormat.Core.Parsing,
        asm: Unity.Localization}
      data:
        m_OpeningBrace: 123
        m_ClosingBrace: 125
        m_Settings:
          rid: 3133919383155638283
        m_AlphanumericSelectors: 1
        m_AllowedSelectorChars: _-
        m_Operators: '[]().,'
        m_AlternativeEscaping: 0
        m_AlternativeEscapeChar: 92
    - rid: 3133919383155638285
      type: {class: ListFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - list
        - l
        - 
        m_SmartSettings:
          rid: 3133919383155638283
    - rid: 3133919383155638286
      type: {class: PersistentVariablesSource, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Groups: []
    - rid: 3133919383155638287
      type: {class: DictionarySource, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data: 
    - rid: 3133919383155638288
      type: {class: ValueTupleSource, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data: 
    - rid: 3133919383155638289
      type: {class: XmlSource, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data: 
    - rid: 3133919383155638290
      type: {class: ReflectionSource, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data: 
    - rid: 3133919383155638291
      type: {class: DefaultSource, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data: 
    - rid: 3133919383155638292
      type: {class: PluralLocalizationFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - plural
        - p
        - 
        m_DefaultTwoLetterISOLanguageName: en
    - rid: 3133919383155638293
      type: {class: ConditionalFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - conditional
        - cond
        - 
    - rid: 3133919383155638294
      type: {class: TimeFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - timespan
        - time
        - t
        - 
        m_DefaultFormatOptions: 4646
    - rid: 3133919383155638295
      type: {class: XElementFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - xelement
        - xml
        - x
        - 
    - rid: 3133919383155638296
      type: {class: ChooseFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - choose
        - c
        m_SplitChar: 124
    - rid: 3133919383155638297
      type: {class: SubStringFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - substr
        m_ParameterDelimiter: 44
        m_NullDisplayString: (null)
        m_OutOfRangeBehavior: 0
    - rid: 3133919383155638298
      type: {class: IsMatchFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - ismatch
    - rid: 3133919383155638299
      type: {class: DefaultFormatter, ns: UnityEngine.Localization.SmartFormat.Extensions,
        asm: Unity.Localization}
      data:
        m_Names:
        - default
        - d
        - 
    - rid: 3133919383155638301
      type: {class: SpecificLocaleSelector, ns: UnityEngine.Localization.Settings,
        asm: Unity.Localization}
      data:
        m_LocaleId:
          m_Code: zh
